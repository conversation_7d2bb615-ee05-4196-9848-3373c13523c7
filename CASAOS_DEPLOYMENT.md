# CasaOS Deployment Guide for Stock Analyzer

This guide will help you deploy the Stock Analyzer application on CasaOS.

## Prerequisites

1. CasaOS installed and running
2. Docker Hub account (for pushing the image)
3. Internet connection for pulling the image

## Step 1: Deploy to Docker Hub

First, let's build and push your image to Docker Hub:

```bash
# Login to Docker Hub
docker login

# Build and deploy using the script
chmod +x deploy.sh
./deploy.sh aabsb1010 latest
```

Or manually:
```bash
# Build the image
docker build -t stocks-analyzer .

# Tag for Docker Hub
docker tag stocks-analyzer aabsb1010/stocks-analyzer:latest

# Push to Docker Hub
docker push aabsb1010/stocks-analyzer:latest
```

## Step 2: Deploy on CasaOS

### Method 1: Using CasaOS App Store (Custom App)

1. **Open CasaOS Dashboard**
2. **Go to App Store**
3. **Click "Custom Install"**
4. **Use Docker Compose** and paste this configuration:

```yaml
name: stocks-analyzer
services:
  stocks-analyzer:
    image: aabsb1010/stocks-analyzer:latest
    restart: unless-stopped
    ports:
      - "8587:8587"
    environment:
      PYTHONUNBUFFERED: "1"
      CHROME_BIN: "/usr/bin/chromium"
      CHROMEDRIVER_PATH: "/usr/bin/chromedriver"
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
```

### Method 2: Using Docker Command in CasaOS Terminal

1. **Open CasaOS Terminal**
2. **Run the following command:**

```bash
docker run -d \
  --name stocks-analyzer \
  --restart unless-stopped \
  -p 8587:8587 \
  -e PYTHONUNBUFFERED=1 \
  -e CHROME_BIN=/usr/bin/chromium \
  -e CHROMEDRIVER_PATH=/usr/bin/chromedriver \
  aabsb1010/stocks-analyzer:latest
```

### Method 3: Using the CasaOS Configuration File

1. Copy the `casaos-app.yml` file to your CasaOS system
2. Use CasaOS CLI or import the configuration

## Configuration Details

### 🔧 **No Volumes Required**
Your Stock Analyzer application is **stateless** - it doesn't need persistent storage because:
- No database files to store
- No user data to persist
- All data is fetched in real-time from external sources
- No configuration files that need to persist

### 🌐 **Environment Variables**
The following environment variables are automatically set:
- `PYTHONUNBUFFERED=1` - Ensures proper logging
- `CHROME_BIN=/usr/bin/chromium` - Chrome browser path
- `CHROMEDRIVER_PATH=/usr/bin/chromedriver` - ChromeDriver path

### 🔌 **Port Configuration**
- **Internal Port**: 8587 (Flask application)
- **External Port**: 8587 (accessible via CasaOS)
- **Protocol**: HTTP

### 💾 **Resource Limits**
Recommended resource allocation:
- **Memory Limit**: 1GB (Chrome can be memory-intensive)
- **Memory Reservation**: 512MB
- **CPU Limit**: 0.5 cores
- **CPU Reservation**: 0.25 cores

## Step 3: Access Your Application

After deployment:

1. **Find your CasaOS IP address** (usually shown in CasaOS dashboard)
2. **Access the application** at: `http://YOUR_CASAOS_IP:8587`
3. **Or use localhost** if accessing from the same machine: `http://localhost:8587`

## Step 4: Using the Application

1. **Enter a stock symbol** (e.g., AAPL, MSFT, GOOGL)
2. **Click Search** to fetch financial data
3. **View interactive charts** with quarterly financial metrics
4. **Analyze trends** in revenue, profit, cash flow, and debt

## Troubleshooting

### Common Issues:

1. **Port Already in Use**
   - Change the external port: `-p 8588:8587`
   - Or stop conflicting services

2. **Memory Issues**
   - Increase memory limit in CasaOS
   - Monitor resource usage in CasaOS dashboard

3. **Chrome/Selenium Issues**
   - The container includes all necessary dependencies
   - Check container logs in CasaOS

### Checking Logs:
In CasaOS dashboard:
1. Go to **Containers**
2. Find **stocks-analyzer**
3. Click **Logs** to view application logs

### Updating the Application:
```bash
# Pull latest version
docker pull aabsb1010/stocks-analyzer:latest

# Restart container (CasaOS will handle this automatically)
```

## Security Notes

- ✅ **No sensitive data** stored in the container
- ✅ **Non-root user** execution for security
- ✅ **Stateless application** - no persistent data
- ✅ **Resource limits** prevent system overload
- ✅ **Health checks** ensure service availability

## Performance Tips

1. **Monitor resource usage** in CasaOS dashboard
2. **Adjust memory limits** if needed (Chrome can use significant memory)
3. **Use specific version tags** instead of 'latest' for production
4. **Regular updates** to get latest features and security fixes

Your Stock Analyzer is now ready for CasaOS deployment! 🚀
