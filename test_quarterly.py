#!/usr/bin/env python3
"""
Test script to debug quarterly data fetching in Docker environment
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_quarterly_data_fetch(ticker="AAPL"):
    """Test quarterly data fetching with detailed logging"""
    
    logger.info(f"Testing quarterly data fetch for {ticker}")
    
    # Chrome options optimized for Docker
    chrome_options = Options()
    chrome_options.add_argument('--headless=new')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--disable-extensions')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--disable-web-security')
    chrome_options.add_argument('--single-process')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

    # Fix permission and cache issues
    import tempfile
    import uuid
    temp_dir = tempfile.mkdtemp()
    unique_id = str(uuid.uuid4())
    chrome_options.add_argument(f'--user-data-dir={temp_dir}/chrome_user_data_{unique_id}')
    chrome_options.add_argument(f'--crash-dumps-dir={temp_dir}/crashes_{unique_id}')
    chrome_options.add_argument('--disable-background-networking')
    chrome_options.add_argument('--disable-default-apps')
    chrome_options.add_argument('--disable-sync')

    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Test URL
        url = f"https://stockanalysis.com/stocks/{ticker}/financials/"
        
        logger.info(f"Navigating to: {url}")
        driver.get(url)
        
        # Execute anti-detection script
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        wait = WebDriverWait(driver, 15)
        
        # Wait for page to load
        logger.info("Waiting for page to load...")
        time.sleep(3)
        
        # Check if quarterly button exists
        try:
            quarterly_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Quarterly')]")
            logger.info(f"Found {len(quarterly_buttons)} quarterly buttons")
            
            if quarterly_buttons:
                button = quarterly_buttons[0]
                logger.info(f"Button text: '{button.text}'")
                logger.info(f"Button is displayed: {button.is_displayed()}")
                logger.info(f"Button is enabled: {button.is_enabled()}")
                
                # Try clicking
                logger.info("Attempting to click quarterly button...")
                try:
                    button.click()
                    logger.info("Standard click successful")
                except Exception as e:
                    logger.warning(f"Standard click failed: {e}")
                    logger.info("Trying JavaScript click...")
                    driver.execute_script("arguments[0].click();", button)
                    logger.info("JavaScript click executed")
                
                # Wait for data to load
                time.sleep(3)
                
                # Check table headers
                try:
                    table = driver.find_element(By.TAG_NAME, "table")
                    headers = table.find_elements(By.TAG_NAME, "th")
                    header_texts = [h.text.strip() for h in headers[1:11]]  # First 10 headers
                    
                    logger.info(f"Table headers after clicking quarterly: {header_texts}")
                    logger.info(f"Total headers found: {len(headers)}")
                    
                    # Check if headers look like quarterly data
                    if len(headers) >= 8:
                        logger.info("✅ Successfully retrieved quarterly data!")
                        return True
                    else:
                        logger.warning("⚠️ May still be showing annual data")
                        return False
                        
                except Exception as e:
                    logger.error(f"Error reading table: {e}")
                    return False
            else:
                logger.error("No quarterly button found!")
                return False
                
        except Exception as e:
            logger.error(f"Error finding quarterly button: {e}")
            return False
            
    except Exception as e:
        logger.error(f"General error: {e}")
        return False
        
    finally:
        driver.quit()

if __name__ == "__main__":
    success = test_quarterly_data_fetch("AAPL")
    if success:
        print("✅ Quarterly data test PASSED")
    else:
        print("❌ Quarterly data test FAILED")
