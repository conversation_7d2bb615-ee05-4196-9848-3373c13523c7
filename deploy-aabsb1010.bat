@echo off
echo Building Stock Analyzer for Docker Hub...
echo Username: aabsb1010

echo.
echo Step 1: Building Docker image...
docker build -t stocks-analyzer .

echo.
echo Step 2: Tagging for Docker Hub...
docker tag stocks-analyzer aabsb1010/stocks-analyzer:latest

echo.
echo Step 3: Testing locally...
echo Starting test container on port 8588...
docker run -d -p 8588:8587 --name stocks-analyzer-test stocks-analyzer

echo Waiting for container to start...
timeout /t 15 /nobreak > nul

echo Testing application...
curl -f http://localhost:8588/ > nul 2>&1
if %errorlevel% == 0 (
    echo SUCCESS: Application is responding!
    docker stop stocks-analyzer-test > nul
    docker rm stocks-analyzer-test > nul
) else (
    echo WARNING: Could not test application, but continuing...
    docker stop stocks-analyzer-test > nul 2>&1
    docker rm stocks-analyzer-test > nul 2>&1
)

echo.
echo Step 4: Ready to push to Docker Hub
echo Please run: docker login
echo Then run: docker push aabsb1010/stocks-analyzer:latest
echo.
echo For CasaOS deployment, use this Docker run command:
echo docker run -d --name stocks-analyzer --restart unless-stopped -p 8587:8587 aabsb1010/stocks-analyzer:latest
echo.
echo For network access from other machines (like *************), the application will be available at:
echo http://YOUR_CASAOS_IP:8587
echo.
pause
