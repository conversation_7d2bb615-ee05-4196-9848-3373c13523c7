name: stocks-analyzer
services:
  stocks-analyzer:
    image: aabsb1010/stocks-analyzer:latest
    restart: unless-stopped
    ports:
      - target: 8587
        published: "8587"
        protocol: tcp
    environment:
      PYTHONUNBUFFERED: "1"
      CHROME_BIN: "/usr/bin/chromium"
      CHROMEDRIVER_PATH: "/usr/bin/chromedriver"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8587/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    # No volumes needed - application is stateless
    # volumes: []
    
    # Network configuration
    networks:
      - casaos
    
    # Labels for CasaOS UI
    labels:
      casaos.name: "Stock Analyzer"
      casaos.description: "Financial data visualization and analysis tool"
      casaos.category: "Finance"
      casaos.port: "8587"
      casaos.scheme: "http"
      casaos.web_ui: "true"
      casaos.icon: "https://raw.githubusercontent.com/walkxcode/dashboard-icons/main/svg/stocks.svg"

networks:
  casaos:
    name: casaos
    external: true
