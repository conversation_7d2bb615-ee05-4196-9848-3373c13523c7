# Docker Hub Deployment Guide

This guide will help you deploy your Stock Analyzer application to Docker Hub and run it on any Linux system.

## Prerequisites

1. **Docker Account**: Create an account at [hub.docker.com](https://hub.docker.com)
2. **Docker Desktop**: Install Docker Desktop on your development machine
3. **Git**: For version control (optional but recommended)

## Step 1: Login to Docker Hub

```bash
docker login
```
Enter your Docker Hub username and password when prompted.

## Step 2: Build and Test Locally

```bash
# Build the image
docker build -t stocks-analyzer .

# Test locally
docker run -p 8587:8587 stocks-analyzer
```

Visit `http://localhost:8587` to verify the application works.

## Step 3: Tag and Push to Docker Hub

Replace `yourusername` with your actual Docker Hub username:

```bash
# Tag the image
docker tag stocks-analyzer yourusername/stocks-analyzer:latest

# Push to Docker Hub
docker push yourusername/stocks-analyzer:latest
```

## Step 4: Automated Deployment (Recommended)

Use the provided deployment script:

```bash
# Make the script executable (Linux/Mac)
chmod +x deploy.sh

# Run the deployment script
./deploy.sh yourusername latest
```

## Step 5: Deploy on Any Linux Server

### Option 1: Direct Docker Run
```bash
docker pull yourusername/stocks-analyzer:latest
docker run -d -p 8587:8587 --name stocks-analyzer yourusername/stocks-analyzer:latest
```

### Option 2: Using Docker Compose
1. Copy `docker-compose.prod.yml` to your server
2. Update the image name with your Docker Hub username
3. Run:
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## Environment Configuration

The Docker image is configured with:
- **Python 3.11** runtime
- **Chromium browser** for web scraping
- **Non-root user** for security
- **Health checks** for monitoring
- **Resource limits** for production use

## Monitoring and Maintenance

### Check container status:
```bash
docker ps
docker logs stocks-analyzer
```

### Update to latest version:
```bash
docker pull yourusername/stocks-analyzer:latest
docker stop stocks-analyzer
docker rm stocks-analyzer
docker run -d -p 8587:8587 --name stocks-analyzer yourusername/stocks-analyzer:latest
```

### Using Docker Compose for updates:
```bash
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

## Troubleshooting

### Common Issues:

1. **Chrome/Selenium Issues**: The image includes all necessary dependencies for headless Chrome
2. **Permission Issues**: The container runs as non-root user for security
3. **Memory Issues**: Increase Docker memory limits if needed
4. **Network Issues**: Ensure port 8587 is accessible

### Debug Commands:
```bash
# Check container logs
docker logs stocks-analyzer

# Access container shell
docker exec -it stocks-analyzer /bin/bash

# Check resource usage
docker stats stocks-analyzer
```

## Security Notes

- Container runs as non-root user
- No sensitive data is stored in the image
- Health checks ensure service availability
- Resource limits prevent resource exhaustion

## Production Recommendations

1. Use a reverse proxy (nginx) for SSL termination
2. Set up monitoring and alerting
3. Regular backups of any persistent data
4. Use specific version tags instead of 'latest' for production
5. Implement log rotation and monitoring
