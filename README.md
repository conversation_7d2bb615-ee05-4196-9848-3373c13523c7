# Stock Financial Data Analyzer

A Flask-based web application that scrapes and visualizes quarterly financial data for publicly traded stocks.

## Features

- 📊 Interactive financial data visualization using Chart.js
- 🔍 Real-time data scraping from stockanalysis.com
- 📱 Responsive design for desktop and mobile
- 🐳 Docker support for easy deployment
- 🔒 Secure containerized environment

## Quick Start with Docker

### Pull from Docker Hub
```bash
docker pull yourusername/stocks-analyzer:latest
docker run -p 8587:8587 yourusername/stocks-analyzer:latest
```

### Build locally
```bash
git clone <your-repo-url>
cd stocks-analyzer
docker build -t stocks-analyzer .
docker run -p 8587:8587 stocks-analyzer
```

### Using Docker Compose
```bash
docker-compose up -d
```

## Usage

1. Open your browser to `http://localhost:8587`
2. Enter a stock symbol (e.g., AAPL, MSFT, GOOGL)
3. Click "Search" to fetch and visualize the data
4. View quarterly financial metrics including:
   - Revenue
   - Gross Profit
   - Net Income
   - Free Cash Flow
   - Total Liabilities
   - Total Debt

## API Endpoints

- `GET /` - Web interface
- `GET /api/quarterly/<symbol>` - J<PERSON>N API for quarterly financial data

## Environment Variables

- `PYTHONUNBUFFERED=1` - Ensures Python output is sent straight to terminal
- `CHROME_BIN` - Path to Chrome binary (set automatically in Docker)
- `CHROMEDRIVER_PATH` - Path to ChromeDriver (set automatically in Docker)

## Requirements

- Python 3.11+
- Chrome/Chromium browser
- ChromeDriver
- Required Python packages (see requirements.txt)

## Docker Hub Deployment

See deployment instructions below for pushing to Docker Hub.

## License

MIT License
