#!/bin/bash

# Production startup script for Stock Analyzer

echo "Starting Stock Analyzer..."

# Check if we're in production mode
if [ "$FLASK_ENV" = "production" ] || [ "$PRODUCTION" = "true" ]; then
    echo "Starting in PRODUCTION mode with <PERSON><PERSON>..."
    exec gunicorn --bind 0.0.0.0:8587 --workers 2 --timeout 120 --keep-alive 2 --max-requests 1000 --max-requests-jitter 100 app:app
else
    echo "Starting in DEVELOPMENT mode with Flask..."
    exec python app.py
fi
