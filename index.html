<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Financial Data Visualization</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;  /* Added padding */
        }
        .search-box {
            text-align: center;
            margin: 20px 0;
        }
        input {
            padding: 10px;
            width: 200px;
            font-size: 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;  /* Added margin for mobile */
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            border-radius: 4px;
            width: auto;  /* Changed from fixed width */
        }
        button:hover {
            background-color: #45a049;
        }
        .chart-container {
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 400px;
            margin-top: 20px;
            width: 100%;  /* Added width */
            min-height: 300px;  /* Added min-height */
        }

        /* Media queries for mobile devices */
        @media screen and (max-width: 768px) {
            body {
                margin: 10px;
            }
            
            .container {
                padding: 0 10px;
            }
            
            .search-box {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }
            
            input {
                width: 100%;
                max-width: 300px;
                margin-bottom: 10px;
            }
            
            button {
                width: 100%;
                max-width: 300px;
            }
            
            .chart-container {
                height: 500px;  /* Taller on mobile for better visibility */
                padding: 10px;
            }
        }

        .toggle-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
        }
        input:checked + .slider {
            background-color: #2196F3;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .slider.round {
            border-radius: 34px;
        }
        .slider.round:before {
            border-radius: 50%;
        }
        .switch input:disabled + .slider {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .switch input:disabled + .slider:before {
            cursor: not-allowed;
        }
        #toggleLabel.error {
            color: #ff6b6b;
            font-size: 0.9em;
        }

        /* Loading spinner styles */
        .loader {
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Adjust loader position for mobile */
        @media screen and (max-width: 768px) {
            .loader {
                width: 14px;
                height: 14px;
            }
        }

        .profile-box {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-top: 20px;
        }

        .profile-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .profile-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .profile-item .label {
            font-weight: bold;
            color: #666;
            font-size: 0.9em;
        }

        .profile-item .value {
            color: #2196F3;
            font-size: 1.1em;
        }

        .description-box {
            margin-top: 15px;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }

        .description-content {
            max-height: 100px;
            overflow-y: auto;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-size: 0.9em;
            line-height: 1.5;
        }

        /* Scrollbar styling */
        .description-content::-webkit-scrollbar {
            width: 8px;
        }

        .description-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .description-content::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .description-content::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        @media screen and (max-width: 768px) {
            .profile-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .description-content {
                max-height: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="search-box">
            <input type="text" id="stockSymbol" placeholder="Enter stock symbol (e.g., AAPL)">
            <button onclick="fetchData()">Search</button>
            <div class="toggle-container">
                <label class="switch">
                    <input type="checkbox" id="dataToggle" disabled>
                    <span class="slider round"></span>
                </label>
                <span id="toggleLabel">Annual</span>
                <div id="quarterlyLoader" class="loader" style="display: none;"></div>
            </div>
        </div>
        <div class="chart-container" style="height: 600px; margin-top: 20px;">
            <canvas id="combinedChart"></canvas>
        </div>
        
        <div class="profile-box">
            <div class="profile-grid">
                <div class="profile-item">
                    <span class="label">Company:</span>
                    <span id="companyName" class="value">-</span>
                </div>
                <div class="profile-item">
                    <span class="label">Industry:</span>
                    <span id="industry" class="value">-</span>
                </div>
                <div class="profile-item">
                    <span class="label">Stock Price:</span>
                    <span id="stockPrice" class="value">-</span>
                </div>
                <div class="profile-item">
                    <span class="label">Market Cap:</span>
                    <span id="marketCap" class="value">-</span>
                </div>
            </div>
            <div class="description-box">
                <span class="label">Description:</span>
                <div id="description" class="description-content">-</div>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>









