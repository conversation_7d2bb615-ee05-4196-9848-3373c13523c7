const API_KEY = 'vfFUB9xSdfoVvjJDvkkx4B15H5MNpTAw';
let charts = {};
let symbol;
let annualData = {};
let quarterlyData = {};

async function fetchData() {
    symbol = document.getElementById('stockSymbol').value.toUpperCase();
    if (!symbol) {
        alert('Please enter a stock symbol');
        return;
    }
    
    try {
        // Show loading state
        document.getElementById('combinedChart').style.opacity = '0.5';
        
        // Reset profile information
        document.getElementById('companyName').textContent = '-';
        document.getElementById('industry').textContent = '-';
        document.getElementById('stockPrice').textContent = '-';
        document.getElementById('marketCap').textContent = '-';
        document.getElementById('description').textContent = '-';
        
        // Fetch profile data
        const profileResponse = await fetch(`https://financialmodelingprep.com/api/v3/profile/${symbol}?apikey=${API_KEY}`);
        const profileData = await profileResponse.json();
        
        if (profileData && profileData[0]) {
            // Update profile information
            document.getElementById('companyName').textContent = profileData[0].companyName;
            document.getElementById('industry').textContent = profileData[0].industry;
            document.getElementById('stockPrice').textContent = `$${profileData[0].price.toFixed(2)}`;
            document.getElementById('marketCap').textContent = formatMarketCap(profileData[0].mktCap);
            document.getElementById('description').textContent = profileData[0].description;
        }
        
        // Fetch annual data first
        const annualResponse = await fetchAnnualData();
        annualData = {
            incomeData: annualResponse[0],
            cashFlowData: annualResponse[1],
            balanceSheetData: annualResponse[2]
        };
        
        // Update chart with annual data immediately
        document.getElementById('dataToggle').checked = false; // Ensure annual view is selected
        updateChart();
        document.getElementById('combinedChart').style.opacity = '1';
        
        // Show quarterly data loader
        document.getElementById('quarterlyLoader').style.display = 'block';
        
        // Fetch quarterly data in the background
        fetchQuarterlyData()
            .then(response => {
                quarterlyData = response;
                // Enable the toggle button once quarterly data is available
                document.getElementById('dataToggle').disabled = false;
                document.getElementById('quarterlyLoader').style.display = 'none';
                console.log('Quarterly data loaded successfully');
            })
            .catch(error => {
                console.error('Error loading quarterly data:', error);
                // Hide loader and show error state
                document.getElementById('quarterlyLoader').style.display = 'none';
                const toggleLabel = document.getElementById('toggleLabel');
                toggleLabel.textContent = 'Annual (Quarterly Unavailable)';
                toggleLabel.style.color = '#ff6b6b';
                document.getElementById('dataToggle').disabled = true;
            });
            
    } catch (error) {
        console.error('Detailed error:', error);
        document.getElementById('combinedChart').style.opacity = '1';
        document.getElementById('quarterlyLoader').style.display = 'none';
        document.getElementById('stockPrice').textContent = '-';
        document.getElementById('marketCap').textContent = '-';
        alert(error.message || 'Failed to fetch data. Please check the stock symbol and try again.');
    }
}

async function fetchAnnualData() {
    const responses = await Promise.all([
        fetch(`https://financialmodelingprep.com/api/v3/income-statement/${symbol}?apikey=${API_KEY}`),
        fetch(`https://financialmodelingprep.com/api/v3/cash-flow-statement/${symbol}?apikey=${API_KEY}`),
        fetch(`https://financialmodelingprep.com/api/v3/balance-sheet-statement/${symbol}?apikey=${API_KEY}`)
    ]);

    for (let response of responses) {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
    }

    return await Promise.all(responses.map(res => res.json()));
}

async function fetchQuarterlyData() {
    try {
        const response = await fetch(`/api/quarterly/${symbol}`);
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        if (data.error) {
            throw new Error(data.error);
        }
        return data;
    } catch (error) {
        console.error('Error fetching quarterly data:', error);
        throw new Error(`Failed to fetch quarterly data: ${error.message}`);
    }
}

function updateChart() {
    const isQuarterly = document.getElementById('dataToggle').checked;
    const data = isQuarterly ? quarterlyData : annualData;
    
    if (!data || !data.incomeData) {
        console.error('Invalid data format:', data);
        return;
    }
    
    // Destroy existing chart
    if (charts.combined) {
        charts.combined.destroy();
    }

    createCombinedChart(
        data.incomeData,
        data.cashFlowData,
        data.balanceSheetData,
        isQuarterly
    );
}

function createCombinedChart(incomeData, cashFlowData, balanceSheetData, isQuarterly) {
    const ctx = document.getElementById('combinedChart').getContext('2d');
    
    // Update toggle label
    document.getElementById('toggleLabel').textContent = isQuarterly ? 'Quarterly' : 'Annual';
    
    // Get the dates from income data and reverse them
    const labels = incomeData.map(item => item.date).reverse();

    const chartData = {
        labels: labels,
        datasets: [
            {
                label: 'Revenue',
                data: incomeData.map(item => item.revenue).reverse(),
                backgroundColor: 'rgba(54, 162, 235, 0.7)',
                borderColor: 'rgb(54, 162, 235)',
                borderWidth: 1
            },
            {
                label: 'Gross Profit',
                data: incomeData.map(item => item.grossProfit).reverse(),
                backgroundColor: 'rgba(153, 102, 255, 0.7)',
                borderColor: 'rgb(153, 102, 255)',
                borderWidth: 1
            },
            {
                label: 'Net Income',
                data: incomeData.map(item => item.netIncome).reverse(),
                backgroundColor: 'rgba(128, 128, 128, 0.7)',
                borderColor: 'rgb(128, 128, 128)',
                borderWidth: 1
            },
            {
                label: 'Total Debt',
                data: balanceSheetData.map(item => item.totalDebt).reverse(),
                backgroundColor: 'rgba(255, 99, 132, 0.7)',
                borderColor: 'rgb(255, 99, 132)',
                borderWidth: 1
            },
            {
                label: 'Total Liabilities',
                data: balanceSheetData.map(item => item.totalLiabilities).reverse(),
                backgroundColor: 'rgba(255, 159, 64, 0.7)',
                borderColor: 'rgb(255, 159, 64)',
                borderWidth: 1
            },
            {
                label: 'Free Cash Flow',
                data: cashFlowData.map(item => item.freeCashFlow).reverse(),
                backgroundColor: 'rgba(75, 192, 192, 0.7)',
                borderColor: 'rgb(75, 192, 192)',
                borderWidth: 1
            }
        ]
    };

    // Create the chart with your existing options
    charts.combined = new Chart(ctx, {
        type: 'bar',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            // Shorter number format for mobile
                            if (window.innerWidth <= 768) {
                                return '$' + (value / 1000000000).toFixed(1) + 'B';
                            }
                            return '$' + (value / 1000000000).toFixed(2) + 'B';
                        },
                        font: {
                            size: window.innerWidth <= 768 ? 10 : 12
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            size: window.innerWidth <= 768 ? 10 : 12
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    position: window.innerWidth <= 768 ? 'bottom' : 'top',
                    labels: {
                        boxWidth: window.innerWidth <= 768 ? 12 : 40,
                        font: {
                            size: window.innerWidth <= 768 ? 10 : 12
                        }
                    }
                },
                title: {
                    display: true,
                    text: `Financial Metrics for ${symbol} (${isQuarterly ? 'Quarterly' : 'Annual'})`,
                    font: {
                        size: window.innerWidth <= 768 ? 14 : 16
                    }
                }
            },
            barPercentage: window.innerWidth <= 768 ? 1 : 0.9,
            categoryPercentage: window.innerWidth <= 768 ? 0.9 : 0.8
        }
    });
}

// Add event listeners
document.getElementById('stockSymbol').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        fetchData();
    }
});

document.getElementById('dataToggle').addEventListener('change', updateChart);

window.addEventListener('resize', () => {
    if (charts.combined) {
        updateChart();
    }
});

// Update the toggle initialization
document.addEventListener('DOMContentLoaded', function() {
    const toggleButton = document.getElementById('dataToggle');
    // Disable toggle initially until quarterly data is loaded
    toggleButton.disabled = true;
    toggleButton.checked = false;
    // Ensure loader is hidden initially
    document.getElementById('quarterlyLoader').style.display = 'none';
});

// Add this helper function to format market cap
function formatMarketCap(value) {
    if (value >= 1e12) {
        return `$${(value / 1e12).toFixed(2)}T`;
    } else if (value >= 1e9) {
        return `$${(value / 1e9).toFixed(2)}B`;
    } else if (value >= 1e6) {
        return `$${(value / 1e6).toFixed(2)}M`;
    } else {
        return `$${value.toFixed(2)}`;
    }
}














