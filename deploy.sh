#!/bin/bash

# Docker Hub deployment script for Stock Analyzer
# Usage: ./deploy.sh <docker-hub-username> [tag]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if username is provided
if [ -z "$1" ]; then
    echo -e "${RED}Error: Docker Hub username is required${NC}"
    echo "Usage: $0 <docker-hub-username> [tag]"
    echo "Example: $0 myusername latest"
    exit 1
fi

DOCKER_USERNAME=$1
TAG=${2:-latest}
IMAGE_NAME="stocks-analyzer"
FULL_IMAGE_NAME="$DOCKER_USERNAME/$IMAGE_NAME:$TAG"

echo -e "${YELLOW}Building Docker image...${NC}"
docker build -t $IMAGE_NAME .

echo -e "${YELLOW}Tagging image for Docker Hub...${NC}"
docker tag $IMAGE_NAME $FULL_IMAGE_NAME

echo -e "${YELLOW}Testing the image locally...${NC}"
echo "Starting container on port 8588 for testing..."
CONTAINER_ID=$(docker run -d -p 8588:8587 $IMAGE_NAME)

# Wait for container to start
sleep 10

# Test if the application is responding
if curl -f http://localhost:8588/ > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Container test passed!${NC}"
    docker stop $CONTAINER_ID > /dev/null
    docker rm $CONTAINER_ID > /dev/null
else
    echo -e "${RED}✗ Container test failed!${NC}"
    docker stop $CONTAINER_ID > /dev/null
    docker rm $CONTAINER_ID > /dev/null
    exit 1
fi

echo -e "${YELLOW}Pushing to Docker Hub...${NC}"
echo "Please make sure you're logged in to Docker Hub (docker login)"
read -p "Press Enter to continue or Ctrl+C to cancel..."

docker push $FULL_IMAGE_NAME

echo -e "${GREEN}✓ Successfully deployed to Docker Hub!${NC}"
echo -e "${GREEN}Image: $FULL_IMAGE_NAME${NC}"
echo ""
echo "To run the image:"
echo "docker run -p 8587:8587 $FULL_IMAGE_NAME"
echo ""
echo "To run with docker-compose, update the image name in docker-compose.yml:"
echo "image: $FULL_IMAGE_NAME"
