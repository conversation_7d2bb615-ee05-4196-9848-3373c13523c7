from flask import Flask, jsonify, send_from_directory
from stock_scraper import scrape_financial_data
import logging
import os

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add route for serving static files
@app.route('/')
def serve_index():
    return send_from_directory('.', 'index.html')

@app.route('/<path:path>')
def serve_static(path):
    return send_from_directory('.', path)

@app.route('/api/quarterly/<symbol>')
def get_quarterly_data(symbol):
    logger.info(f"Received request for symbol: {symbol}")
    
    try:
        df = scrape_financial_data(symbol)
        if df is None:
            logger.error(f"Failed to fetch data for symbol: {symbol}")
            return jsonify({'error': 'Failed to fetch data'}), 404
        
        # Convert DataFrame to lists for each metric
        metrics_map = {
            'Revenue': [],
            'Gross Profit': [],
            'Net Income': [],
            'Free Cash Flow': [],
            'Total Liabilities': [],
            'Total Debt': []
        }
        
        dates = df.columns[1:].tolist()
        
        for _, row in df.iterrows():
            metric = row['Metric']
            if metric in metrics_map:
                metrics_map[metric] = row[1:].tolist()
        
        # Verify all required metrics are present
        missing_metrics = [metric for metric, values in metrics_map.items() if not values]
        if missing_metrics:
            logger.error(f"Missing metrics for {symbol}: {missing_metrics}")
            return jsonify({'error': f'Missing required metrics: {", ".join(missing_metrics)}'}), 404
        
        quarterly_data = {
            'incomeData': [
                {
                    'date': date,
                    'revenue': metrics_map['Revenue'][i],
                    'grossProfit': metrics_map['Gross Profit'][i],
                    'netIncome': metrics_map['Net Income'][i]
                }
                for i, date in enumerate(dates)
            ],
            'cashFlowData': [
                {
                    'date': date,
                    'freeCashFlow': metrics_map['Free Cash Flow'][i]
                }
                for i, date in enumerate(dates)
            ],
            'balanceSheetData': [
                {
                    'date': date,
                    'totalLiabilities': metrics_map['Total Liabilities'][i],
                    'totalDebt': metrics_map['Total Debt'][i]
                }
                for i, date in enumerate(dates)
            ]
        }
        
        logger.info(f"Successfully processed data for {symbol}")
        return jsonify(quarterly_data)
        
    except Exception as e:
        logger.error(f"Error processing request for {symbol}: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

if __name__ == '__main__':
    import os
    # Use debug mode only in development
    debug_mode = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    app.run(host='0.0.0.0', port=8587, debug=debug_mode)

