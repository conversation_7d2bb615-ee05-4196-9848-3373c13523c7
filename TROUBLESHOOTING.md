# Troubleshooting Guide for Stock Analyzer

## Common Issues and Solutions

### 1. **Quarterly Data Shows "Annual (Quarterly Unavailable)" in Docker**

**Problem**: The application works on Windows but shows annual data only in Docker.

**Solution**: This has been fixed in the latest version with:
- Enhanced Chrome options for Docker compatibility
- Multiple fallback methods for button clicking
- Better anti-detection measures
- Unique user data directories to prevent conflicts

**Update to latest version**:
```bash
docker pull aabsb1010/stocks-analyzer:latest
```

### 2. **Permission Denied Errors in Docker**

**Problem**: 
```
WARNING:selenium.webdriver.common.selenium_manager:Cache folder (/home/<USER>/.cache/selenium) cannot be created: Permission denied
```

**Solution**: Fixed in latest Docker image with:
- Proper directory permissions
- Unique temporary directories for each Chrome instance
- Non-root user with correct permissions

### 3. **Session Not Created Exception**

**Problem**:
```
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: probably user data directory is already in use
```

**Solution**: Fixed with unique user data directories:
- Each Chrome instance gets a unique temporary directory
- Automatic cleanup of temporary files
- No conflicts between multiple instances

### 4. **Development Server Warning**

**Problem**:
```
WARNING: This is a development server. Do not use it in a production deployment.
```

**Solution**: The Docker image now uses Gunicorn for production:
- Production-ready WSGI server
- Better performance and stability
- Proper worker management

### 5. **Network Access Issues**

**Problem**: Can't access from other machines on network (e.g., *************).

**Solution**: 
1. **Check Docker port binding**:
   ```bash
   docker run -p 8587:8587 aabsb1010/stocks-analyzer:latest
   ```

2. **Check firewall settings** on the host machine
3. **Verify network connectivity**:
   ```bash
   # From another machine
   curl http://*************:8587
   ```

4. **For CasaOS**: The application should be accessible at `http://CASAOS_IP:8587`

### 6. **Memory Issues**

**Problem**: Container crashes or becomes unresponsive.

**Solution**: Increase memory allocation:
```yaml
deploy:
  resources:
    limits:
      memory: 1G  # Increase if needed
```

### 7. **Chrome Crashes in Docker**

**Problem**: Chrome browser crashes inside container.

**Solution**: Use the enhanced Chrome options (already included):
- `--no-sandbox`
- `--disable-dev-shm-usage`
- `--single-process`
- Unique user data directories

## Testing Commands

### Test the Application
```bash
# Test locally
curl http://localhost:8587

# Test from network
curl http://*************:8587

# Test API endpoint
curl http://localhost:8587/api/quarterly/AAPL
```

### Debug Container
```bash
# Check container logs
docker logs stocks-analyzer

# Access container shell
docker exec -it stocks-analyzer /bin/bash

# Check Chrome version
docker exec -it stocks-analyzer chromium --version

# Test quarterly data fetching
docker exec -it stocks-analyzer python test_quarterly.py
```

### Monitor Resources
```bash
# Check resource usage
docker stats stocks-analyzer

# Check container status
docker ps | grep stocks-analyzer
```

## Performance Optimization

### 1. **Memory Settings**
- Minimum: 512MB
- Recommended: 1GB
- Heavy usage: 2GB

### 2. **CPU Settings**
- Minimum: 0.25 cores
- Recommended: 0.5 cores
- Heavy usage: 1 core

### 3. **Network Optimization**
- Use local DNS resolution
- Consider caching proxy for external requests
- Monitor network latency to stockanalysis.com

## Deployment Best Practices

### 1. **Use Specific Tags**
Instead of `latest`, use specific version tags:
```bash
docker pull aabsb1010/stocks-analyzer:v1.2.0
```

### 2. **Health Checks**
The container includes built-in health checks:
```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8587/"]
  interval: 30s
  timeout: 10s
  retries: 3
```

### 3. **Restart Policies**
Always use restart policies:
```bash
docker run --restart unless-stopped aabsb1010/stocks-analyzer:latest
```

### 4. **Log Management**
Configure log rotation:
```bash
docker run --log-opt max-size=10m --log-opt max-file=3 aabsb1010/stocks-analyzer:latest
```

## Getting Help

If you encounter issues not covered here:

1. **Check container logs** first
2. **Test with the debug script**: `python test_quarterly.py`
3. **Verify network connectivity**
4. **Check resource usage**
5. **Try restarting the container**

## Version History

- **v1.0**: Initial release
- **v1.1**: Fixed quarterly data issues in Docker
- **v1.2**: Added production server, fixed permissions
- **Current**: Enhanced stability and error handling
