import time
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import StaleElementReferenceException, TimeoutException
import pandas as pd
from selenium.webdriver.chrome.options import Options
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

#v1.1.1
def convert_value(value_str):
    """Convert string values to numeric, handling different formats"""
    if not value_str or value_str == '-':
        return 0
    
    # Handle percentage values
    if '%' in value_str:
        return float(value_str.replace('%', ''))
    
    # Remove any commas and spaces
    value_str = value_str.replace(',', '').strip()
    
    try:
        # Convert to float and multiply by 1000 (million to thousand)
        return float(value_str) * 1000
    except ValueError:
        return value_str

def get_table_data(driver, wait):
    try:
        # Wait for table to be present
        table = wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
        
        # Wait a bit for the table to be fully loaded and stable
        time.sleep(1)
        
        # Get headers and rows
        header_cells = table.find_elements(By.TAG_NAME, "th")
        if len(header_cells) <= 1:
            raise ValueError("Not enough header cells found")
            
        headers = [cell.text.strip() for cell in header_cells[1:21]]  # Get first 20 quarters
        
        # Get rows with explicit wait
        rows = wait.until(EC.presence_of_all_elements_located((By.TAG_NAME, "tr")))[1:]
        
        if not headers or not rows:
            raise ValueError("No headers or rows found")
            
        return headers, rows
        
    except Exception as e:
        print(f"Error in get_table_data: {e}")
        raise

def scrape_url(url, metrics, chrome_options):
    driver = None
    try:
        # Initialize Chrome driver with better error handling
        driver = webdriver.Chrome(options=chrome_options)

        # Execute script to avoid detection
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # Set page load timeout
        driver.set_page_load_timeout(30)

        driver.get(url)
        wait = WebDriverWait(driver, 15)  # Increased timeout

        # Try multiple methods to click the quarterly button
        try:
            # Method 1: Standard click
            quarterly_button = wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//button[contains(text(), 'Quarterly')]")))
            quarterly_button.click()
        except Exception as e:
            logger.warning(f"Standard click failed, trying JavaScript click: {e}")
            try:
                # Method 2: JavaScript click
                quarterly_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Quarterly')]")
                driver.execute_script("arguments[0].click();", quarterly_button)
            except Exception as e2:
                logger.warning(f"JavaScript click failed, trying alternative selector: {e2}")
                try:
                    # Method 3: Alternative selector
                    quarterly_button = driver.find_element(By.CSS_SELECTOR, "button[data-period='quarterly'], button:contains('Quarterly')")
                    driver.execute_script("arguments[0].click();", quarterly_button)
                except Exception as e3:
                    logger.error(f"All click methods failed: {e3}")
                    # Continue anyway, might still get some data

        time.sleep(2)  # Increased wait time
        
        headers, rows = get_table_data(driver, wait)

        # Check if we successfully got quarterly data by examining headers
        if headers:
            # Quarterly data typically has more periods and different date formats
            logger.info(f"Data headers: {headers[:5]}...")  # Show first 5 headers
            if len(headers) >= 8:  # Quarterly should have at least 8 quarters
                logger.info("Successfully retrieved quarterly data")
            else:
                logger.warning(f"May have retrieved annual data instead (only {len(headers)} periods)")

        # Debug: Print all available metrics
        all_metrics = [row.find_elements(By.TAG_NAME, "td")[0].text.strip()
                      for row in rows if len(row.find_elements(By.TAG_NAME, "td")) >= 1]
        logger.info(f"Available metrics on page {url}: {len(all_metrics)} metrics found")
        if all_metrics:
            logger.info(f"Sample metrics: {all_metrics[:3]}...")  # Show first 3 metrics
        
        data = []
        try:
            data = [
                [metric_name] + [convert_value(col.text.strip()) for col in cols[1:21]]
                for row in rows
                if len(cols := row.find_elements(By.TAG_NAME, "td")) >= 21
                and (metric_name := cols[0].text.strip()) in metrics
            ]
        except StaleElementReferenceException:
            time.sleep(0.5)
            data = [
                [metric_name] + [convert_value(col.text.strip()) for col in cols[1:21]]
                for row in rows
                if len(cols := row.find_elements(By.TAG_NAME, "td")) >= 21
                and (metric_name := cols[0].text.strip()) in metrics
            ]
        
        if not data:
            print(f"No data found for metrics {metrics} at {url}")
        else:
            print(f"Found data for metrics: {[row[0] for row in data]}")
            
        return headers, data
    except Exception as e:
        print(f"Error in scrape_url for {url}: {e}")
        return None, None
    finally:
        driver.quit()

def scrape_financial_data(ticker="AAPL"):
    logger.info(f"Starting to scrape data for {ticker}")

    chrome_options = Options()
    chrome_options.add_argument('--headless=new')  # Use new headless mode
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--disable-extensions')
    chrome_options.add_argument('--disable-infobars')
    chrome_options.add_argument('--log-level=3')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--disable-web-security')
    chrome_options.add_argument('--disable-features=VizDisplayCompositor')
    chrome_options.add_argument('--disable-background-timer-throttling')
    chrome_options.add_argument('--disable-backgrounding-occluded-windows')
    chrome_options.add_argument('--disable-renderer-backgrounding')
    chrome_options.add_argument('--disable-features=TranslateUI')
    chrome_options.add_argument('--disable-ipc-flooding-protection')
    chrome_options.add_argument('--single-process')  # Important for Docker
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_argument('--disable-features=VizDisplayCompositor')

    # Fix permission and cache issues
    import tempfile
    import uuid
    temp_dir = tempfile.mkdtemp()
    unique_id = str(uuid.uuid4())
    chrome_options.add_argument(f'--user-data-dir={temp_dir}/chrome_user_data_{unique_id}')
    chrome_options.add_argument(f'--crash-dumps-dir={temp_dir}/crashes_{unique_id}')
    chrome_options.add_argument('--disable-background-networking')
    chrome_options.add_argument('--disable-default-apps')
    chrome_options.add_argument('--disable-sync')

    # Add user agent to avoid detection (Linux compatible)
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

    # Add experimental options to avoid detection
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    urls = {
        f"https://stockanalysis.com/stocks/{ticker}/financials/cash-flow-statement/": 
            ['Net Income', 'Free Cash Flow'],
        f"https://stockanalysis.com/stocks/{ticker}/financials/": 
            ['Revenue', 'Gross Profit', 'Revenue Growth'],
        f"https://stockanalysis.com/stocks/{ticker}/financials/balance-sheet/": 
            ['Total Liabilities', 'Total Debt']  # Add 'Total Debt' here
    }

    all_data = []
    headers = None

    try:
        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=3) as executor:
            # Create a list to store future objects
            future_to_url = {
                executor.submit(scrape_url, url, metrics, chrome_options): url 
                for url, metrics in urls.items()
            }

            # Process completed futures as they finish
            for future in future_to_url:
                url = future_to_url[future]
                try:
                    logger.info(f"Processing URL: {url}")
                    url_headers, url_data = future.result()
                    if url_headers and url_data:
                        if headers is None:
                            headers = url_headers
                        all_data.extend(url_data)
                    else:
                        logger.error(f"No data returned for {url}")
                except Exception as e:
                    logger.error(f"Error processing {url}: {str(e)}")

        if not all_data or not headers:
            logger.error("No data collected")
            return None

        df = pd.DataFrame(all_data, columns=['Metric'] + headers)
        logger.info(f"Successfully scraped data for {ticker}")
        return df

    except Exception as e:
        logger.error(f"Error in scrape_financial_data: {str(e)}")
        return None

if __name__ == "__main__":
    start_time = time.time()
    print(f"Starting scraping for AAPL")
    df = scrape_financial_data("AAPL")
    
    if df is not None:
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_rows', None)
        print("\nSelected Financial Metrics (Quarterly):")
        print(df)
        print(f"\nTotal execution time: {time.time() - start_time:.2f} seconds")
    else:
        print("\nFailed to collect financial data")
