import time
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import StaleElementReferenceException, TimeoutException
import pandas as pd
from selenium.webdriver.chrome.options import Options
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

#v1.1.1
def convert_value(value_str):
    """Convert string values to numeric, handling different formats"""
    if not value_str or value_str == '-':
        return 0
    
    # Handle percentage values
    if '%' in value_str:
        return float(value_str.replace('%', ''))
    
    # Remove any commas and spaces
    value_str = value_str.replace(',', '').strip()
    
    try:
        # Convert to float and multiply by 1000 (million to thousand)
        return float(value_str) * 1000
    except ValueError:
        return value_str

def get_table_data(driver, wait):
    try:
        # Wait for table to be present
        table = wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
        
        # Wait a bit for the table to be fully loaded and stable
        time.sleep(1)
        
        # Get headers and rows
        header_cells = table.find_elements(By.TAG_NAME, "th")
        if len(header_cells) <= 1:
            raise ValueError("Not enough header cells found")
            
        headers = [cell.text.strip() for cell in header_cells[1:21]]  # Get first 20 quarters
        
        # Get rows with explicit wait
        rows = wait.until(EC.presence_of_all_elements_located((By.TAG_NAME, "tr")))[1:]
        
        if not headers or not rows:
            raise ValueError("No headers or rows found")
            
        return headers, rows
        
    except Exception as e:
        print(f"Error in get_table_data: {e}")
        raise

def scrape_url(url, metrics, chrome_options):
    driver = webdriver.Chrome(options=chrome_options)
    try:
        driver.get(url)
        wait = WebDriverWait(driver, 10)
        
        quarterly_button = wait.until(EC.element_to_be_clickable(
            (By.XPATH, "//button[contains(text(), 'Quarterly')]")))
        quarterly_button.click()
        time.sleep(1)
        
        headers, rows = get_table_data(driver, wait)
        
        # Debug: Print all available metrics
        all_metrics = [row.find_elements(By.TAG_NAME, "td")[0].text.strip() 
                      for row in rows if len(row.find_elements(By.TAG_NAME, "td")) >= 1]
        print(f"Available metrics on page {url}:")
        print('\n'.join(all_metrics))
        
        data = []
        try:
            data = [
                [metric_name] + [convert_value(col.text.strip()) for col in cols[1:21]]
                for row in rows
                if len(cols := row.find_elements(By.TAG_NAME, "td")) >= 21
                and (metric_name := cols[0].text.strip()) in metrics
            ]
        except StaleElementReferenceException:
            time.sleep(0.5)
            data = [
                [metric_name] + [convert_value(col.text.strip()) for col in cols[1:21]]
                for row in rows
                if len(cols := row.find_elements(By.TAG_NAME, "td")) >= 21
                and (metric_name := cols[0].text.strip()) in metrics
            ]
        
        if not data:
            print(f"No data found for metrics {metrics} at {url}")
        else:
            print(f"Found data for metrics: {[row[0] for row in data]}")
            
        return headers, data
    except Exception as e:
        print(f"Error in scrape_url for {url}: {e}")
        return None, None
    finally:
        driver.quit()

def scrape_financial_data(ticker="AAPL"):
    logger.info(f"Starting to scrape data for {ticker}")

    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--disable-extensions')
    chrome_options.add_argument('--disable-infobars')
    chrome_options.add_argument('--log-level=3')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--blink-settings=imagesEnabled=false')
    chrome_options.add_argument('--disable-background-timer-throttling')
    chrome_options.add_argument('--disable-backgrounding-occluded-windows')
    chrome_options.add_argument('--disable-renderer-backgrounding')
    chrome_options.add_argument('--disable-features=TranslateUI')
    chrome_options.add_argument('--disable-ipc-flooding-protection')
    chrome_options.add_argument('--remote-debugging-port=9222')

    # Add user agent to avoid detection (Linux compatible)
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    urls = {
        f"https://stockanalysis.com/stocks/{ticker}/financials/cash-flow-statement/": 
            ['Net Income', 'Free Cash Flow'],
        f"https://stockanalysis.com/stocks/{ticker}/financials/": 
            ['Revenue', 'Gross Profit', 'Revenue Growth'],
        f"https://stockanalysis.com/stocks/{ticker}/financials/balance-sheet/": 
            ['Total Liabilities', 'Total Debt']  # Add 'Total Debt' here
    }

    all_data = []
    headers = None

    try:
        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=3) as executor:
            # Create a list to store future objects
            future_to_url = {
                executor.submit(scrape_url, url, metrics, chrome_options): url 
                for url, metrics in urls.items()
            }

            # Process completed futures as they finish
            for future in future_to_url:
                url = future_to_url[future]
                try:
                    logger.info(f"Processing URL: {url}")
                    url_headers, url_data = future.result()
                    if url_headers and url_data:
                        if headers is None:
                            headers = url_headers
                        all_data.extend(url_data)
                    else:
                        logger.error(f"No data returned for {url}")
                except Exception as e:
                    logger.error(f"Error processing {url}: {str(e)}")

        if not all_data or not headers:
            logger.error("No data collected")
            return None

        df = pd.DataFrame(all_data, columns=['Metric'] + headers)
        logger.info(f"Successfully scraped data for {ticker}")
        return df

    except Exception as e:
        logger.error(f"Error in scrape_financial_data: {str(e)}")
        return None

if __name__ == "__main__":
    start_time = time.time()
    print(f"Starting scraping for AAPL")
    df = scrape_financial_data("AAPL")
    
    if df is not None:
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_rows', None)
        print("\nSelected Financial Metrics (Quarterly):")
        print(df)
        print(f"\nTotal execution time: {time.time() - start_time:.2f} seconds")
    else:
        print("\nFailed to collect financial data")
